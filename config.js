// API Configuration
const CONFIG = {
    // API Settings
    API_BASE_URL: '/plugsec/api/v1',
    API_TIMEOUT: 30000, // 30 seconds
    
    // File Upload Settings
    MAX_FILE_SIZE: 50 * 1024 * 1024, // 50MB
    ALLOWED_EXTENSIONS: ['plugin', 'py'],
    ALLOWED_MIME_TYPES: [
        'text/x-python',
        'text/x-python-script',
        'text/plain',
        'application/octet-stream'
    ],
    
    // UI Settings
    TOAST_DURATION: 5000,
    DEBOUNCE_DELAY: 300,
    ANIMATION_DURATION: 300,
    
    // Search Settings
    DEFAULT_SEARCH_LIMIT: 25,
    MAX_SEARCH_LIMIT: 100,
    SEARCH_HISTORY_LIMIT: 10,
    
    // Pagination
    DEFAULT_PAGE_SIZE: 25,
    MAX_PAGE_SIZE: 100,
    
    // Local Storage Keys
    STORAGE_KEYS: {
        AUTH_TOKEN: 'authToken',
        API_KEY: 'apiKey',
        USER_PROFILE: 'userProfile',
        SEARCH_HISTORY: 'searchHistory',
        THEME_PREFERENCE: 'themePreference',
        LANGUAGE_PREFERENCE: 'languagePreference'
    },
    
    // Theme Settings
    THEMES: {
        LIGHT: 'light',
        DARK: 'dark',
        AUTO: 'auto'
    },
    
    // Supported Languages
    LANGUAGES: {
        RU: 'ru',
        EN: 'en'
    },
    
    // API Endpoints
    ENDPOINTS: {
        // Authentication
        LOGIN_TELEGRAM: '/auth/telegram',
        
        // User Profile
        USER_ME: '/users/me',
        USER_HISTORY: '/users/me/history',
        
        // Reports
        REPORT_BY_HASH: '/report/{hash}',
        REPORTS_BATCH: '/reports/batch',
        REPORTS_SEARCH: '/reports/search',
        
        // Statistics
        GLOBAL_STATS: '/stats',
        DEVELOPER_STATS: '/developer/{id}/stats',
        CHANNEL_STATS: '/channel/{id}/stats',
        
        // Scanning
        SCAN_UPLOAD: '/scan/upload'
    },
    
    // Error Messages
    ERROR_MESSAGES: {
        NETWORK_ERROR: 'Ошибка сети. Проверьте подключение к интернету.',
        UNAUTHORIZED: 'Необходима авторизация для выполнения этого действия.',
        FORBIDDEN: 'Недостаточно прав для выполнения этого действия.',
        NOT_FOUND: 'Запрашиваемые данные не найдены.',
        TOO_MANY_REQUESTS: 'Слишком много запросов. Попробуйте позже.',
        SERVER_ERROR: 'Ошибка сервера. Попробуйте позже.',
        FILE_TOO_LARGE: 'Файл слишком большой. Максимальный размер: {size}MB',
        INVALID_FILE_TYPE: 'Неподдерживаемый тип файла.',
        UPLOAD_FAILED: 'Ошибка загрузки файла.',
        INVALID_HASH: 'Некорректный формат хэша.',
        EMPTY_SEARCH: 'Введите поисковый запрос.',
        INVALID_ID: 'Некорректный ID.',
        LOGIN_FAILED: 'Ошибка авторизации через Telegram.',
        TOKEN_INVALID: 'Токен авторизации недействителен.',
        SESSION_EXPIRED: 'Сессия истекла. Пожалуйста, войдите снова.',
        AUTH_DATA_INVALID: 'Неверные данные авторизации.',
        BOT_NOT_CONFIGURED: 'Telegram бот не настроен. Обратитесь к администратору.'
    },
    
    // Success Messages
    SUCCESS_MESSAGES: {
        FILE_UPLOADED: 'Файл успешно загружен и обрабатывается.',
        SEARCH_COMPLETED: 'Поиск завершен.',
        PROFILE_LOADED: 'Профиль пользователя загружен.',
        LOGOUT_SUCCESS: 'Вы успешно вышли из системы.',
        DATA_SAVED: 'Данные сохранены.',
        LOGIN_SUCCESS: 'Успешная авторизация через Telegram!',
        TOKEN_VALIDATED: 'Токен авторизации действителен.'
    },
    
    // Validation Rules
    VALIDATION: {
        HASH_MIN_LENGTH: 32,
        HASH_MAX_LENGTH: 128,
        SEARCH_QUERY_MIN_LENGTH: 2,
        SEARCH_QUERY_MAX_LENGTH: 100,
        ID_MIN_VALUE: 1,
        ID_MAX_VALUE: 999999999
    },
    
    // Security Settings
    SECURITY: {
        ENABLE_CSP: true,
        ENABLE_HTTPS_ONLY: true,
        SESSION_TIMEOUT: 24 * 60 * 60 * 1000, // 24 hours
        MAX_LOGIN_ATTEMPTS: 5,
        LOCKOUT_DURATION: 15 * 60 * 1000 // 15 minutes
    },

    // Telegram Bot Settings
    TELEGRAM: {
        BOT_USERNAME: 'YOUR_BOT_USERNAME', // Замените на имя вашего бота
        WIDGET_SIZE: 'large',
        WIDGET_RADIUS: 10,
        REQUEST_ACCESS: 'write',
        RETURN_TO: window.location.origin // URL для возврата после авторизации
    },
    
    // Feature Flags
    FEATURES: {
        ENABLE_DARK_MODE: true,
        ENABLE_SEARCH_HISTORY: true,
        ENABLE_AUTO_COMPLETE: true,
        ENABLE_FILE_PREVIEW: true,
        ENABLE_BATCH_OPERATIONS: true,
        ENABLE_EXPORT: true,
        ENABLE_NOTIFICATIONS: true,
        ENABLE_ANALYTICS: false
    },
    
    // Performance Settings
    PERFORMANCE: {
        ENABLE_CACHING: true,
        CACHE_DURATION: 5 * 60 * 1000, // 5 minutes
        ENABLE_COMPRESSION: true,
        LAZY_LOAD_IMAGES: true,
        VIRTUAL_SCROLLING_THRESHOLD: 100
    },
    
    // Development Settings
    DEVELOPMENT: {
        ENABLE_DEBUG: false,
        ENABLE_MOCK_API: false,
        LOG_LEVEL: 'info', // 'debug', 'info', 'warn', 'error'
        ENABLE_PERFORMANCE_MONITORING: false
    }
};

// Utility functions for configuration
const ConfigUtils = {
    // Get configuration value with fallback
    get(path, fallback = null) {
        return path.split('.').reduce((obj, key) => 
            obj && obj[key] !== undefined ? obj[key] : fallback, CONFIG);
    },
    
    // Check if feature is enabled
    isFeatureEnabled(feature) {
        return this.get(`FEATURES.${feature}`, false);
    },
    
    // Get API endpoint with parameter substitution
    getEndpoint(name, params = {}) {
        let endpoint = this.get(`ENDPOINTS.${name}`, '');
        
        // Replace parameters in endpoint
        Object.keys(params).forEach(key => {
            endpoint = endpoint.replace(`{${key}}`, params[key]);
        });
        
        return endpoint;
    },
    
    // Get error message with parameter substitution
    getErrorMessage(key, params = {}) {
        let message = this.get(`ERROR_MESSAGES.${key}`, 'Произошла ошибка');
        
        // Replace parameters in message
        Object.keys(params).forEach(key => {
            message = message.replace(`{${key}}`, params[key]);
        });
        
        return message;
    },
    
    // Get success message
    getSuccessMessage(key) {
        return this.get(`SUCCESS_MESSAGES.${key}`, 'Операция выполнена успешно');
    },
    
    // Validate configuration
    validate() {
        const required = [
            'API_BASE_URL',
            'ENDPOINTS.LOGIN_TELEGRAM',
            'ENDPOINTS.USER_ME',
            'ENDPOINTS.GLOBAL_STATS'
        ];
        
        const missing = required.filter(path => !this.get(path));
        
        if (missing.length > 0) {
            console.error('Missing required configuration:', missing);
            return false;
        }
        
        return true;
    },
    
    // Get theme preference
    getTheme() {
        const saved = localStorage.getItem(CONFIG.STORAGE_KEYS.THEME_PREFERENCE);
        return saved || CONFIG.THEMES.AUTO;
    },
    
    // Set theme preference
    setTheme(theme) {
        if (Object.values(CONFIG.THEMES).includes(theme)) {
            localStorage.setItem(CONFIG.STORAGE_KEYS.THEME_PREFERENCE, theme);
            this.applyTheme(theme);
        }
    },
    
    // Apply theme to document
    applyTheme(theme) {
        const root = document.documentElement;
        
        if (theme === CONFIG.THEMES.AUTO) {
            // Use system preference
            const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
            theme = prefersDark ? CONFIG.THEMES.DARK : CONFIG.THEMES.LIGHT;
        }
        
        root.setAttribute('data-theme', theme);
    },
    
    // Initialize configuration
    init() {
        // Validate configuration
        if (!this.validate()) {
            throw new Error('Invalid configuration');
        }
        
        // Apply theme
        this.applyTheme(this.getTheme());
        
        // Listen for system theme changes
        if (this.getTheme() === CONFIG.THEMES.AUTO) {
            window.matchMedia('(prefers-color-scheme: dark)')
                .addEventListener('change', (e) => {
                    this.applyTheme(CONFIG.THEMES.AUTO);
                });
        }
        
        console.log('Configuration initialized successfully');
    }
};

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, ConfigUtils };
} else {
    window.CONFIG = CONFIG;
    window.ConfigUtils = ConfigUtils;
}

// Auto-initialize when loaded
document.addEventListener('DOMContentLoaded', () => {
    try {
        ConfigUtils.init();
    } catch (error) {
        console.error('Failed to initialize configuration:', error);
    }
});
