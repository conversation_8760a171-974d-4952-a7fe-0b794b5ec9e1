<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Telegram Auth Demo - MK Plugin Security</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .demo-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            text-align: center;
        }
        
        .demo-header {
            margin-bottom: 30px;
        }
        
        .demo-header h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .demo-header p {
            color: #666;
            font-size: 16px;
        }
        
        .auth-section {
            margin: 30px 0;
            padding: 20px;
            border: 2px dashed #ddd;
            border-radius: 10px;
        }
        
        .auth-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .telegram-widget-container {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        
        .manual-form {
            text-align: left;
            max-width: 400px;
            margin: 0 auto;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            color: #333;
            font-weight: bold;
        }
        
        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .btn {
            background: #0088cc;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #006699;
        }
        
        .result-section {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            text-align: left;
        }
        
        .result-section h3 {
            color: #333;
            margin-bottom: 15px;
        }
        
        .result-data {
            background: #fff;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #ddd;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
            word-break: break-all;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .instructions {
            background: #e7f3ff;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
        }
        
        .instructions h4 {
            color: #0066cc;
            margin-bottom: 10px;
        }
        
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🔐 Telegram Auth Demo</h1>
            <p>Демонстрация авторизации через Telegram для MK Plugin Security</p>
        </div>
        
        <div class="instructions">
            <h4>Инструкции для тестирования:</h4>
            <ol>
                <li>Убедитесь, что ваш Telegram бот настроен правильно</li>
                <li>Обновите BOT_USERNAME в коде ниже</li>
                <li>Используйте HTTPS или localhost для тестирования</li>
                <li>Проверьте консоль браузера для отладочной информации</li>
            </ol>
        </div>
        
        <div class="auth-section">
            <h3>Метод 1: Telegram Login Widget</h3>
            <div class="telegram-widget-container" id="telegram-widget-container">
                <!-- Telegram виджет будет вставлен здесь -->
            </div>
            <button class="btn" onclick="initTelegramWidget()">Инициализировать виджет</button>
        </div>
        
        <div class="auth-section">
            <h3>Метод 2: Ручной ввод данных</h3>
            <form class="manual-form" onsubmit="testManualAuth(event)">
                <div class="form-group">
                    <label for="demo-id">Telegram ID:</label>
                    <input type="number" id="demo-id" name="id" placeholder="123456789" required>
                </div>
                <div class="form-group">
                    <label for="demo-first-name">Имя:</label>
                    <input type="text" id="demo-first-name" name="first_name" placeholder="Иван" required>
                </div>
                <div class="form-group">
                    <label for="demo-username">Username:</label>
                    <input type="text" id="demo-username" name="username" placeholder="ivan_user">
                </div>
                <div class="form-group">
                    <label for="demo-auth-date">Auth Date:</label>
                    <input type="number" id="demo-auth-date" name="auth_date" readonly>
                </div>
                <div class="form-group">
                    <label for="demo-hash">Hash:</label>
                    <input type="text" id="demo-hash" name="hash" placeholder="abc123..." required>
                </div>
                <button type="submit" class="btn">Тестировать авторизацию</button>
            </form>
        </div>
        
        <div class="result-section">
            <h3>Результат авторизации:</h3>
            <div id="auth-status" class="status" style="display: none;"></div>
            <div id="auth-result" class="result-data">Данные авторизации появятся здесь...</div>
        </div>
    </div>

    <script async src="https://telegram.org/js/telegram-widget.js?22"></script>
    <script>
        // Конфигурация (обновите BOT_USERNAME)
        const BOT_USERNAME = 'YOUR_BOT_USERNAME'; // Замените на имя вашего бота
        const API_BASE_URL = '/plugsec/api/v1'; // Или ваш API URL
        
        // Устанавливаем текущую дату
        document.getElementById('demo-auth-date').value = Math.floor(Date.now() / 1000);
        
        // Инициализация Telegram виджета
        function initTelegramWidget() {
            const container = document.getElementById('telegram-widget-container');
            container.innerHTML = '';
            
            if (BOT_USERNAME === 'YOUR_BOT_USERNAME') {
                showStatus('error', 'Пожалуйста, обновите BOT_USERNAME в коде');
                return;
            }
            
            const script = document.createElement('script');
            script.async = true;
            script.src = 'https://telegram.org/js/telegram-widget.js?22';
            script.setAttribute('data-telegram-login', BOT_USERNAME);
            script.setAttribute('data-size', 'large');
            script.setAttribute('data-radius', '10');
            script.setAttribute('data-onauth', 'onTelegramAuth(user)');
            script.setAttribute('data-request-access', 'write');
            
            container.appendChild(script);
            showStatus('success', 'Telegram виджет инициализирован');
        }
        
        // Обработчик авторизации через виджет
        window.onTelegramAuth = function(user) {
            console.log('Telegram auth data:', user);
            showAuthResult(user);
            testApiAuth(user);
        };
        
        // Ручная авторизация
        function testManualAuth(event) {
            event.preventDefault();
            
            const formData = new FormData(event.target);
            const authData = Object.fromEntries(formData.entries());
            
            // Преобразуем числовые поля
            authData.id = parseInt(authData.id);
            authData.auth_date = parseInt(authData.auth_date);
            
            console.log('Manual auth data:', authData);
            showAuthResult(authData);
            testApiAuth(authData);
        }
        
        // Тестирование API авторизации
        async function testApiAuth(authData) {
            try {
                showStatus('success', 'Отправка данных на сервер...');
                
                const response = await fetch(`${API_BASE_URL}/auth/telegram`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(authData)
                });
                
                const result = await response.json();
                
                if (response.ok) {
                    showStatus('success', 'Авторизация успешна!');
                    showAuthResult({
                        ...authData,
                        api_response: result
                    });
                } else {
                    showStatus('error', `Ошибка API: ${result.detail || 'Неизвестная ошибка'}`);
                }
                
            } catch (error) {
                console.error('API Error:', error);
                showStatus('error', `Ошибка сети: ${error.message}`);
            }
        }
        
        // Отображение результата
        function showAuthResult(data) {
            const resultDiv = document.getElementById('auth-result');
            resultDiv.textContent = JSON.stringify(data, null, 2);
        }
        
        // Отображение статуса
        function showStatus(type, message) {
            const statusDiv = document.getElementById('auth-status');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            statusDiv.style.display = 'block';
        }
        
        // Автоматическая инициализация при загрузке
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Demo page loaded');
            console.log('Bot username:', BOT_USERNAME);
            console.log('API URL:', API_BASE_URL);
        });
    </script>
</body>
</html>
