# Настройка Telegram авторизации для MK Plugin Security

## Обзор

Этот документ описывает процесс настройки авторизации через Telegram для веб-приложения MK Plugin Security.

## Предварительные требования

1. Telegram аккаунт
2. Доступ к @BotFather в Telegram
3. Веб-сервер с поддержкой HTTPS (обязательно для Telegram Login Widget)

## Шаг 1: Создание Telegram бота

1. Откройте Telegram и найдите @BotFather
2. Отправьте команду `/newbot`
3. Следуйте инструкциям для создания нового бота:
   - Введите имя бота (например: "MK Plugin Security Bot")
   - Введите username бота (например: "mkpluginsecurity_bot")
4. Сохраните полученный токен бота

## Шаг 2: Настройка домена для Login Widget

1. Отправьте @BotFather команду `/setdomain`
2. Выберите вашего бота
3. Введите домен вашего сайта (например: `example.com` или `localhost` для тестирования)

**Важно:** Telegram Login Widget работает только с HTTPS, кроме localhost.

## Шаг 3: Настройка конфигурации

Откройте файл `config.js` и обновите настройки Telegram:

```javascript
TELEGRAM: {
    BOT_USERNAME: 'your_bot_username', // Замените на username вашего бота
    WIDGET_SIZE: 'large',
    WIDGET_RADIUS: 10,
    REQUEST_ACCESS: 'write',
    RETURN_TO: window.location.origin
},
```

## Шаг 4: Обновление серверной части

Убедитесь, что ваш API сервер поддерживает эндпоинт `/api/v1/auth/telegram`:

### Пример обработки авторизации (Python/FastAPI):

```python
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
import hashlib
import hmac
from typing import Optional

router = APIRouter()

class TelegramAuthData(BaseModel):
    id: int
    first_name: str
    last_name: Optional[str] = None
    username: Optional[str] = None
    photo_url: Optional[str] = None
    auth_date: int
    hash: str

@router.post("/auth/telegram")
async def telegram_auth(auth_data: TelegramAuthData):
    # Проверка подлинности данных от Telegram
    bot_token = "YOUR_BOT_TOKEN"  # Токен от BotFather
    
    # Создаем строку для проверки
    data_check_string = "\n".join([
        f"{key}={value}" for key, value in sorted(auth_data.dict().items()) 
        if key != "hash" and value is not None
    ])
    
    # Вычисляем hash
    secret_key = hashlib.sha256(bot_token.encode()).digest()
    calculated_hash = hmac.new(secret_key, data_check_string.encode(), hashlib.sha256).hexdigest()
    
    if calculated_hash != auth_data.hash:
        raise HTTPException(status_code=400, detail="Invalid auth data")
    
    # Создаем JWT токен для пользователя
    access_token = create_access_token(data={"sub": str(auth_data.id)})
    
    return {"access_token": access_token, "token_type": "bearer"}
```

## Шаг 5: Тестирование

1. Откройте ваше веб-приложение
2. Нажмите кнопку "Войти через Telegram"
3. Выберите один из методов авторизации:
   - **Telegram Widget** (автоматический)
   - **Ручной ввод данных** (для отладки)

### Получение данных для ручного ввода:

1. Откройте вашего бота в Telegram
2. Отправьте команду `/start`
3. Бот должен вернуть данные авторизации в формате JSON

## Возможные проблемы и решения

### 1. Ошибка "Bot domain invalid"
- Убедитесь, что домен правильно настроен в @BotFather
- Проверьте, что используется HTTPS (кроме localhost)

### 2. Виджет не загружается
- Проверьте правильность username бота в config.js
- Убедитесь, что скрипт telegram-widget.js загружается

### 3. Ошибка проверки hash
- Проверьте правильность токена бота на сервере
- Убедитесь, что данные передаются без изменений

### 4. CORS ошибки
- Настройте CORS на сервере для вашего домена
- Добавьте необходимые заголовки

## Безопасность

1. **Никогда не передавайте токен бота на клиент**
2. **Всегда проверяйте hash данных от Telegram**
3. **Используйте HTTPS в продакшене**
4. **Установите короткое время жизни JWT токенов**
5. **Логируйте все попытки авторизации**

## Дополнительные возможности

### Автоматическое обновление токена:
```javascript
// В script.js можно добавить автоматическое обновление токена
setInterval(async () => {
    if (authToken) {
        const isValid = await validateToken();
        if (!isValid) {
            await refreshToken();
        }
    }
}, 30 * 60 * 1000); // Каждые 30 минут
```

### Расширенная информация о пользователе:
```javascript
// Сохранение дополнительной информации
const userData = {
    id: telegramData.id,
    first_name: telegramData.first_name,
    last_name: telegramData.last_name,
    username: telegramData.username,
    photo_url: telegramData.photo_url,
    language_code: telegramData.language_code
};
```

## Поддержка

Если у вас возникли проблемы с настройкой:

1. Проверьте логи сервера
2. Используйте инструменты разработчика браузера
3. Проверьте настройки бота в @BotFather
4. Убедитесь в правильности конфигурации

## Полезные ссылки

- [Telegram Login Widget Documentation](https://core.telegram.org/widgets/login)
- [Telegram Bot API](https://core.telegram.org/bots/api)
- [@BotFather](https://t.me/botfather)
