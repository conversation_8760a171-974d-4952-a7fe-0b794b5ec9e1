# MK Plugin Security - Веб-интерфейс

Современный веб-интерфейс для системы безопасности плагинов MK Plugin Security Bot с элегантным дизайном и плавными анимациями.

## 🚀 Возможности

### 🔍 Поиск и анализ
- **Поиск по хэшу** - быстрый поиск отчетов по хэшу файла
- **Пакетный поиск** - проверка нескольких файлов одновременно
- **Расширенный поиск** - поиск с фильтрами по вердикту, статусу одобрения и другим параметрам
- **Автодополнение** - умные подсказки при вводе поискового запроса

### 📊 Статистика
- **Глобальная статистика** - общие показатели системы
- **Статистика по разработчикам** - детальная информация о конкретных разработчиках
- **Статистика по каналам** - анализ плагинов из различных каналов
- **Интерактивные графики** - визуализация данных в реальном времени

### 📁 Сканирование файлов
- **Drag & Drop загрузка** - простое перетаскивание файлов
- **Прогресс-бар** - отслеживание процесса загрузки
- **Валидация файлов** - проверка типа и размера файлов
- **Мгновенные результаты** - быстрое получение отчетов о безопасности

### 🔐 Авторизация
- **Telegram Login** - безопасная авторизация через Telegram
- **JWT токены** - надежная система аутентификации
- **Профиль пользователя** - персональная информация и история

## 🎨 Дизайн и UX

### Современный интерфейс
- **Градиентные фоны** - красивые цветовые переходы
- **Плавные анимации** - CSS3 анимации и переходы
- **Адаптивный дизайн** - оптимизация для всех устройств
- **Темная тема** - поддержка темного режима

### Интерактивные элементы
- **Hover эффекты** - отзывчивые элементы интерфейса
- **Анимированные иконки** - живые визуальные элементы
- **Пульсирующие индикаторы** - динамические элементы безопасности
- **Smooth scrolling** - плавная прокрутка между секциями

## 🛠 Технологии

### Frontend
- **HTML5** - семантическая разметка
- **CSS3** - современные стили и анимации
- **Vanilla JavaScript** - чистый JavaScript без фреймворков
- **CSS Grid & Flexbox** - современная компоновка
- **CSS Custom Properties** - переменные для темизации

### API Integration
- **RESTful API** - интеграция с MK Plugin Security Bot API
- **JWT Authentication** - безопасная авторизация
- **File Upload** - загрузка файлов с прогрессом
- **Error Handling** - обработка ошибок и уведомления

## 📋 Структура проекта

```
PluginSecurity/
├── index.html          # Главная страница
├── styles.css          # Основные стили
├── script.js           # Основная логика
├── config.js           # Конфигурация
├── README.md           # Документация
└── MKplugSec_doc.json  # API документация
```

## 🚀 Быстрый старт

### 1. Клонирование проекта
```bash
git clone <repository-url>
cd PluginSecurity
```

### 2. Настройка сервера
Для работы с API необходимо настроить веб-сервер (например, nginx) с проксированием запросов к API:

```nginx
location /plugsec/ {
    proxy_pass http://your-api-server/;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
}
```

### 3. Открытие в браузере
Откройте `index.html` в веб-браузере или разместите файлы на веб-сервере.

## ⚙️ Конфигурация

### API Settings
Настройте параметры API в файле `config.js`:

```javascript
const CONFIG = {
    API_BASE_URL: '/plugsec/api/v1',
    API_TIMEOUT: 30000,
    // ... другие настройки
};
```

### Настройка авторизации
Для работы с Telegram авторизацией необходимо:

1. Получить API ключ от @mkultra6969
2. Настроить Telegram Bot для авторизации
3. Указать правильные домены в настройках бота

## 📱 Адаптивность

Сайт полностью адаптирован для различных устройств:

- **Desktop** (1200px+) - полная функциональность
- **Tablet** (768px-1199px) - адаптированный интерфейс
- **Mobile** (до 767px) - мобильная версия

## 🎯 Основные функции

### Поиск отчетов
1. **По хэшу файла** - введите хэш в поле поиска
2. **Пакетный поиск** - вставьте несколько хэшей (по одному на строку)
3. **Расширенный поиск** - используйте фильтры для точного поиска

### Просмотр статистики
1. **Глобальная статистика** - автоматически загружается при открытии
2. **По разработчику** - введите ID разработчика
3. **По каналу** - введите ID канала

### Сканирование файлов
1. **Авторизуйтесь** через Telegram
2. **Перетащите файл** в область загрузки или выберите через кнопку
3. **Дождитесь результата** - файл будет проанализирован

## 🔧 Расширенные возможности

### Горячие клавиши
- `Ctrl/Cmd + K` - фокус на поле поиска
- `Escape` - закрытие модальных окон

### Локальное хранение
- История поиска (последние 10 запросов)
- Настройки темы
- Токены авторизации

### Уведомления
- Toast уведомления для всех действий
- Индикаторы загрузки
- Сообщения об ошибках

## 🛡️ Безопасность

### Защита данных
- JWT токены для авторизации
- Валидация файлов на клиенте
- Защита от XSS атак
- HTTPS рекомендуется для продакшена

### Ограничения
- Максимальный размер файла: 50MB
- Поддерживаемые форматы: только .plugin и .py
- Лимит запросов согласно API

### Поддерживаемые форматы файлов
- **.plugin** - Файлы плагинов (основной формат)
- **.py** - Python плагины и скрипты

## 🐛 Отладка

### Консоль разработчика
Включите отладку в `config.js`:
```javascript
DEVELOPMENT: {
    ENABLE_DEBUG: true,
    LOG_LEVEL: 'debug'
}
```

### Частые проблемы
1. **CORS ошибки** - настройте прокси сервер
2. **401 Unauthorized** - проверьте токен авторизации
3. **Файл не загружается** - проверьте размер и тип файла

## 📞 Поддержка

- **Автор API**: @mkultra6969
- **Версия API**: 0.0.5
- **Документация**: MKplugSec_doc.json

## 📄 Лицензия

Проект создан для работы с MK Plugin Security Bot API.
Все права на API принадлежат автору @mkultra6969.

## 🔄 Обновления

### v1.0.0
- Первоначальный релиз
- Базовая функциональность поиска и статистики
- Современный дизайн с анимациями
- Адаптивная верстка
- Интеграция с Telegram авторизацией
